{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\src\\router\\index.js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\router\\index.js", "mtime": 1748604247127}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICJ2dWUiOwppbXBvcnQgVnVlUm91dGVyIGZyb20gInZ1ZS1yb3V0ZXIiOwppbXBvcnQgSG9tZSBmcm9tICIuLi92aWV3cy9Ib21lLnZ1ZSI7CmltcG9ydCBMb2dpbiBmcm9tICIuLi92aWV3cy9Mb2dpbi52dWUiOwppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOwppbXBvcnQgc3RvcmUgZnJvbSAiLi4vc3RvcmUiOwppbXBvcnQgeyBNZXNzYWdlIH0gZnJvbSAiZWxlbWVudC11aSI7ClZ1ZS51c2UoVnVlUm91dGVyKTsKY29uc3Qgcm91dGVzID0gW3sKICBwYXRoOiAiLyIsCiAgbmFtZTogIiIsCiAgY29tcG9uZW50OiBIb21lLAogIGhpZGRlbjogdHJ1ZSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICIvIiwKICAgIG5hbWU6ICLpppbpobUiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL0Rhc2hib2FyZC52dWUiKQogIH1dCn0sIHsKICBwYXRoOiAiL2xvZ2luIiwKICBuYW1lOiAiTG9naW4iLAogIGNvbXBvbmVudDogTG9naW4sCiAgaGlkZGVuOiB0cnVlLAogIG1ldGE6IHsKICAgIHJlcXVpcmVzQXV0aDogZmFsc2UKICB9Cn0sIHsKICBwYXRoOiAiL2ppY2h1IiwKICBuYW1lOiAi5Z+656GA566h55CGIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL2NvbmZpZyIsCiAgICBuYW1lOiAi5Z+656GA6K6+572uIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy9kYXRhL2NvbmZpZ3MudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2Jhbm5lciIsCiAgICBuYW1lOiAi6L2u5pKt5Zu+IiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy9kYXRhL2Jhbm5lci52dWUiKQogIH0sIHsKICAgIHBhdGg6ICIvbmF2IiwKICAgIG5hbWU6ICLpppbpobXlr7zoiKoiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL2RhdGEvbmF2LnZ1ZSIpCiAgfSwgewogICAgcGF0aDogIi9nb25nZ2FvIiwKICAgIG5hbWU6ICLlhazlkYoiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL2RhdGEvZ29uZ2dhby52dWUiKQogIH0KICAvLyB7CiAgLy8gICBwYXRoOiAiL3ZpcCIsCiAgLy8gICBuYW1lOiAi5Lya5ZGYIiwKICAvLyAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy9kYXRhL3ZpcC52dWUiKSwKICAvLyB9LAogIF0KfSwgewogIHBhdGg6ICIveGlhZGFuIiwKICBuYW1lOiAi6K6i5Y2V566h55CGIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL3R5cGUiLAogICAgbmFtZTogIuacjeWKoeexu+WeiyIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvdGFvY2FuL3R5cGUudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL3Rhb2NhbiIsCiAgICBuYW1lOiAi5aWX6aSQ57G75Z6LIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy90YW9jYW4vdGFvY2FuLnZ1ZSIpCiAgfSwKICAvLyB7CiAgLy8gICBwYXRoOiAiL3lvbmdodSIsCiAgLy8gICBuYW1lOiAi55So5oi35YiX6KGoIiwKICAvLyAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy90YW9jYW4vdXNlci52dWUiKSwKICAvLyB9LAogIHsKICAgIHBhdGg6ICIvZGluZ2RhbiIsCiAgICBuYW1lOiAi562+57qm55So5oi35YiX6KGoIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy90YW9jYW4vZGluZ2Rhbi52dWUiKQogIH0sIHsKICAgIHBhdGg6ICIvcXVuIiwKICAgIG5hbWU6ICLnrb7nuqblrqLmiLfnvqQiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL3lvbmdodS9xdW4udnVlIikKICB9XQp9LCB7CiAgcGF0aDogIi95b25naHUiLAogIG5hbWU6ICLnlKjmiLfnrqHnkIYiLAogIGNvbXBvbmVudDogSG9tZSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICIvdXNlciIsCiAgICBuYW1lOiAi55So5oi3IiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy95b25naHUvdXNlci52dWUiKQogIH0sIHsKICAgIHBhdGg6ICIvZGVidHMiLAogICAgbmFtZTogIuWAuuWKoeS6uuWIl+ihqCIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvZGVidC9kZWJ0cy52dWUiKQogIH1dCn0sIHsKICBwYXRoOiAiL3poaWZ1IiwKICBuYW1lOiAi5pSv5LuY5YiX6KGoIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL29yZGVyIiwKICAgIG5hbWU6ICLmlK/ku5jliJfooagiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL3lvbmdodS9vcmRlci52dWUiKQogIH1dCn0sIHsKICBwYXRoOiAiL2xpYW90aWFuIiwKICBuYW1lOiAi6IGK5aSp5YiX6KGoIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL2NoYXQiLAogICAgbmFtZTogIuiBiuWkqeWIl+ihqCIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMveW9uZ2h1L2NoYXQudnVlIikKICB9XQp9LCB7CiAgcGF0aDogIi93ZW5zaHVndWFubGkiLAogIG5hbWU6ICLmlofkuabnrqHnkIYiLAogIGNvbXBvbmVudDogSG9tZSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICIvZGluZ3poaSIsCiAgICBuYW1lOiAi5ZCI5ZCM5a6a5Yi2IiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy93ZW5zaHUvZGluZ3poaS52dWUiKQogIH0sIHsKICAgIHBhdGg6ICIvc2hlbmhlIiwKICAgIG5hbWU6ICLlkIjlkIzlrqHmoLgiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL3dlbnNodS9zaGVuaGUudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2NhdGUiLAogICAgbmFtZTogIuWQiOWQjOexu+WeiyIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvd2Vuc2h1L2NhdGUudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2hldG9uZyIsCiAgICBuYW1lOiAi5ZCI5ZCM5YiX6KGoIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy93ZW5zaHUvaW5kZXgudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2xhd3llciIsCiAgICBuYW1lOiAi5Y+R5b6L5biI5Ye9IiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy95b25naHUvbGF3eWVyLnZ1ZSIpCiAgfSwgewogICAgcGF0aDogIi9rZWNoZW5nIiwKICAgIG5hbWU6ICLor77nqIvliJfooagiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL3NoaXBpbi9rZWNoZW5nLnZ1ZSIpCiAgfV0KfSwgewogIHBhdGg6ICIveXVhbmdvbmciLAogIG5hbWU6ICLlkZjlt6XnrqHnkIYiLAogIGNvbXBvbmVudDogSG9tZSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICIvemhpd2VpIiwKICAgIG5hbWU6ICLogYzkvY0iLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL3l1YW5nb25nL3poaXdlaS52dWUiKQogIH0sIHsKICAgIHBhdGg6ICIveXVhbmdvbmciLAogICAgbmFtZTogIuWRmOW3pSIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMveXVhbmdvbmcvaW5kZXgudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2x2c2hpIiwKICAgIG5hbWU6ICLlvovluIgiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL2x2c2hpL2x2c2hpLnZ1ZSIpCiAgfSwgewogICAgcGF0aDogIi9xdWFueGlhbiIsCiAgICBuYW1lOiAi5p2D6ZmQ566h55CGIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy95dWFuZ29uZy9xdWFueGlhbi52dWUiKQogIH1dCn0sIHsKICBwYXRoOiAiL2Z1d3UiLAogIG5hbWU6ICLmnI3liqHnrqHnkIYiLAogIGNvbXBvbmVudDogSG9tZSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICIvZnV3dSIsCiAgICBuYW1lOiAi5pyN5Yqh5YiX6KGoIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy9mdXd1L2luZGV4LnZ1ZSIpCiAgfV0KfSwgewogIHBhdGg6ICIveGlud2VuIiwKICBuYW1lOiAi5qGI5L6L566h55CGIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL2FubGkiLAogICAgbmFtZTogIuahiOS+i+WIl+ihqCIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMveGlud2VuL3hpbndlbi52dWUiKQogIH1dCn0sIHsKICBwYXRoOiAiL2x2c2hpZ3VhbmxpIiwKICBuYW1lOiAi5LiT5Lia566h55CGIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL3podWFueWUiLAogICAgbmFtZTogIuS4k+S4muWIl+ihqCIsCiAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvbHZzaGkvemh1YW55ZS52dWUiKQogIH1dCn0sCi8vIOS4quS6uuS/oeaBr+WSjOiuvue9rumhtemdogp7CiAgcGF0aDogIi9wcm9maWxlIiwKICBuYW1lOiAi5Liq5Lq65L+h5oGvIiwKICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvcHJvZmlsZS9pbmRleC52dWUiKSwKICBoaWRkZW46IHRydWUKfSwgewogIHBhdGg6ICIvY2hhbmdlUHdkIiwKICBuYW1lOiAi5L+u5pS55a+G56CBIiwKICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgiLi4vdmlld3MvcGFnZXMvY2hhbmdlUHdkLnZ1ZSIpLAogIGhpZGRlbjogdHJ1ZQp9LCB7CiAgcGF0aDogIi9hcmNoaXZlIiwKICBuYW1lOiAi5b2S5qGj566h55CGIiwKICBjb21wb25lbnQ6IEhvbWUsCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAiL2FyY2hpdmUvZmlsZSIsCiAgICBuYW1lOiAi5paH5Lu25b2S5qGjIiwKICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCIuLi92aWV3cy9wYWdlcy9hcmNoaXZlL0ZpbGUudnVlIikKICB9LCB7CiAgICBwYXRoOiAiL2FyY2hpdmUvc2VhcmNoIiwKICAgIG5hbWU6ICLmoaPmoYjmo4DntKIiLAogICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoIi4uL3ZpZXdzL3BhZ2VzL2FyY2hpdmUvU2VhcmNoLnZ1ZSIpCiAgfV0KfV07CmNvbnN0IHJvdXRlciA9IG5ldyBWdWVSb3V0ZXIoewogIHJvdXRlcwp9KTsKcm91dGVyLmJlZm9yZUVhY2goKHRvLCBmcm9tLCBuZXh0KSA9PiB7CiAgLy8g57qv5YmN56uv5qih5byPIC0g566A5YyW6Lev55Sx5a6I5Y2rCiAgaWYgKHRvLnBhdGggIT0gIi9sb2dpbiIpIHsKICAgIC8vIOajgOafpeaYr+WQpuaciXRva2Vu77yM5aaC5p6c5rKh5pyJ5YiZ6Lez6L2s5Yiw55m75b2V6aG1CiAgICBjb25zdCB0b2tlbiA9IHN0b3JlLmdldHRlcnMuR0VUX1RPS0VOOwogICAgaWYgKCF0b2tlbikgewogICAgICBuZXh0KHsKICAgICAgICBwYXRoOiAiL2xvZ2luIgogICAgICB9KTsKICAgIH0gZWxzZSB7CiAgICAgIG5leHQoKTsKICAgIH0KICB9IGVsc2UgewogICAgbmV4dCgpOwogIH0KfSk7CmV4cG9ydCBkZWZhdWx0IHJvdXRlcjs="}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home", "<PERSON><PERSON>", "axios", "store", "Message", "use", "routes", "path", "name", "component", "hidden", "children", "meta", "requiresAuth", "router", "beforeEach", "to", "from", "next", "token", "getters", "GET_TOKEN"], "sources": ["H:/fdbfront/xinqianduan/src/router/index.js"], "sourcesContent": ["import Vue from \"vue\";\r\nimport VueRouter from \"vue-router\";\r\nimport Home from \"../views/Home.vue\";\r\nimport Login from \"../views/Login.vue\";\r\nimport axios from \"axios\";\r\nimport store from \"../store\";\r\nimport { Message } from \"element-ui\";\r\nVue.use(VueRouter);\r\n\r\nconst routes = [\r\n  {\r\n    path: \"/\",\r\n    name: \"\",\r\n    component: Home,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: \"/\",\r\n        name: \"首页\",\r\n        component: () => import(\"../views/pages/Dashboard.vue\"),\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: \"/login\",\r\n    name: \"Login\",\r\n    component: Login,\r\n    hidden: true,\r\n    meta: {\r\n      requiresAuth: false,\r\n    },\r\n  },\r\n  {\r\n    path: \"/jichu\",\r\n    name: \"基础管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/config\",\r\n        name: \"基础设置\",\r\n        component: () => import(\"../views/pages/data/configs.vue\"),\r\n      },\r\n      {\r\n        path: \"/banner\",\r\n        name: \"轮播图\",\r\n        component: () => import(\"../views/pages/data/banner.vue\"),\r\n      },\r\n      {\r\n        path: \"/nav\",\r\n        name: \"首页导航\",\r\n        component: () => import(\"../views/pages/data/nav.vue\"),\r\n      },\r\n      {\r\n        path: \"/gonggao\",\r\n        name: \"公告\",\r\n        component: () => import(\"../views/pages/data/gonggao.vue\"),\r\n      },\r\n      // {\r\n      //   path: \"/vip\",\r\n      //   name: \"会员\",\r\n      //   component: () => import(\"../views/pages/data/vip.vue\"),\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/xiadan\",\r\n    name: \"订单管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/type\",\r\n        name: \"服务类型\",\r\n        component: () => import(\"../views/pages/taocan/type.vue\"),\r\n      },\r\n      {\r\n        path: \"/taocan\",\r\n        name: \"套餐类型\",\r\n        component: () => import(\"../views/pages/taocan/taocan.vue\"),\r\n      },\r\n      // {\r\n      //   path: \"/yonghu\",\r\n      //   name: \"用户列表\",\r\n      //   component: () => import(\"../views/pages/taocan/user.vue\"),\r\n      // },\r\n      {\r\n        path: \"/dingdan\",\r\n        name: \"签约用户列表\",\r\n        component: () => import(\"../views/pages/taocan/dingdan.vue\"),\r\n      },\r\n      {\r\n        path: \"/qun\",\r\n        name: \"签约客户群\",\r\n        component: () => import(\"../views/pages/yonghu/qun.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/yonghu\",\r\n    name: \"用户管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/user\",\r\n        name: \"用户\",\r\n        component: () => import(\"../views/pages/yonghu/user.vue\"),\r\n      },\r\n      {\r\n        path: \"/debts\",\r\n        name: \"债务人列表\",\r\n        component: () => import(\"../views/pages/debt/debts.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/zhifu\",\r\n    name: \"支付列表\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/order\",\r\n        name: \"支付列表\",\r\n        component: () => import(\"../views/pages/yonghu/order.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/liaotian\",\r\n    name: \"聊天列表\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/chat\",\r\n        name: \"聊天列表\",\r\n        component: () => import(\"../views/pages/yonghu/chat.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/wenshuguanli\",\r\n    name: \"文书管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/dingzhi\",\r\n        name: \"合同定制\",\r\n        component: () => import(\"../views/pages/wenshu/dingzhi.vue\"),\r\n      },\r\n      {\r\n        path: \"/shenhe\",\r\n        name: \"合同审核\",\r\n        component: () => import(\"../views/pages/wenshu/shenhe.vue\"),\r\n      },\r\n      {\r\n        path: \"/cate\",\r\n        name: \"合同类型\",\r\n        component: () => import(\"../views/pages/wenshu/cate.vue\"),\r\n      },\r\n      {\r\n        path: \"/hetong\",\r\n        name: \"合同列表\",\r\n        component: () => import(\"../views/pages/wenshu/index.vue\"),\r\n      },\r\n      {\r\n        path: \"/lawyer\",\r\n        name: \"发律师函\",\r\n        component: () => import(\"../views/pages/yonghu/lawyer.vue\"),\r\n      },\r\n      {\r\n        path: \"/kecheng\",\r\n        name: \"课程列表\",\r\n        component: () => import(\"../views/pages/shipin/kecheng.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/yuangong\",\r\n    name: \"员工管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/zhiwei\",\r\n        name: \"职位\",\r\n        component: () => import(\"../views/pages/yuangong/zhiwei.vue\"),\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工\",\r\n        component: () => import(\"../views/pages/yuangong/index.vue\"),\r\n      },\r\n      {\r\n        path: \"/lvshi\",\r\n        name: \"律师\",\r\n        component: () => import(\"../views/pages/lvshi/lvshi.vue\"),\r\n      },\r\n      {\r\n        path: \"/quanxian\",\r\n        name: \"权限管理\",\r\n        component: () => import(\"../views/pages/yuangong/quanxian.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/fuwu\",\r\n    name: \"服务管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务列表\",\r\n        component: () => import(\"../views/pages/fuwu/index.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/xinwen\",\r\n    name: \"案例管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/anli\",\r\n        name: \"案例列表\",\r\n        component: () => import(\"../views/pages/xinwen/xinwen.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/lvshiguanli\",\r\n    name: \"专业管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/zhuanye\",\r\n        name: \"专业列表\",\r\n        component: () => import(\"../views/pages/lvshi/zhuanye.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  // 个人信息和设置页面\r\n  {\r\n    path: \"/profile\",\r\n    name: \"个人信息\",\r\n    component: () => import(\"../views/pages/profile/index.vue\"),\r\n    hidden: true,\r\n  },\r\n  {\r\n    path: \"/changePwd\",\r\n    name: \"修改密码\",\r\n    component: () => import(\"../views/pages/changePwd.vue\"),\r\n    hidden: true,\r\n  },\r\n  {\r\n    path: \"/archive\",\r\n    name: \"归档管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/archive/file\",\r\n        name: \"文件归档\",\r\n        component: () => import(\"../views/pages/archive/File.vue\"),\r\n      },\r\n      {\r\n        path: \"/archive/search\",\r\n        name: \"档案检索\",\r\n        component: () => import(\"../views/pages/archive/Search.vue\"),\r\n      }\r\n    ]\r\n  },\r\n];\r\n\r\nconst router = new VueRouter({\r\n  routes,\r\n});\r\nrouter.beforeEach((to, from, next) => {\r\n  // 纯前端模式 - 简化路由守卫\r\n  if (to.path != \"/login\") {\r\n    // 检查是否有token，如果没有则跳转到登录页\r\n    const token = store.getters.GET_TOKEN;\r\n    if (!token) {\r\n      next({\r\n        path: \"/login\",\r\n      });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n    next();\r\n  }\r\n});\r\nexport default router;\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,OAAO,QAAQ,YAAY;AACpCN,GAAG,CAACO,GAAG,CAACN,SAAS,CAAC;AAElB,MAAMO,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAET,IAAI;EACfU,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;EACxD,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAER,KAAK;EAChBS,MAAM,EAAE,IAAI;EACZE,IAAI,EAAE;IACJC,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEN,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B;EACvD,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;EAC3D;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEF,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B;EACzD,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B;EACzD,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;EAC3D,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC;EAChE,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B;EACzD,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC;AAEL,CAAC;AACD;AACA;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;EAC3DC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;EACvDC,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAET,IAAI;EACfW,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC;AAEL,CAAC,CACF;AAED,MAAMK,MAAM,GAAG,IAAIf,SAAS,CAAC;EAC3BO;AACF,CAAC,CAAC;AACFQ,MAAM,CAACC,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACA,IAAIF,EAAE,CAACT,IAAI,IAAI,QAAQ,EAAE;IACvB;IACA,MAAMY,KAAK,GAAGhB,KAAK,CAACiB,OAAO,CAACC,SAAS;IACrC,IAAI,CAACF,KAAK,EAAE;MACVD,IAAI,CAAC;QACHX,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACLW,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AACF,eAAeJ,MAAM", "ignoreList": []}]}