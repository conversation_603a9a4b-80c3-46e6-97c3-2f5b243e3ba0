module.exports={A:{A:{"1":"B","2":"K D E F A aC"},B:{"2":"1 2 3 4 5 6 7 8 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I"},C:{"1":"0 L M G N O P GB v w x y z HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB","2":"1 2 3 4 5 6 7 8 9 bC DC J FB K D E F A B C gB hB iB jB kB lB mB nB EC oB FC pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R GC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I 6B HC IC cC dC eC fC"},D:{"1":"0 J FB K D E F A B C L M G N O P GB v w x y z HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB","2":"1 2 3 4 5 6 7 8 9 gB hB iB jB kB lB mB nB EC oB FC pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I 6B HC IC"},E:{"1":"E F A B C kC KC 7B","2":"J FB K D gC JC hC iC jC","129":"L M G 8B lC mC nC LC MC 9B oC AC NC OC PC QC RC pC BC SC TC UC VC WC XC CC qC"},F:{"1":"0 G N O P GB v w x y z HB IB JB KB LB MB NB OB PB QB RB SB TB UB XB ZB 8B","2":"F B C VB WB YB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R GC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u rC sC tC uC 7B YC vC"},G:{"1":"E 0C 1C 2C 3C 4C 5C 6C 7C","2":"JC wC ZC xC yC zC","257":"8C 9C AD BD CD DD ED FD LC MC 9B GD AC NC OC PC QC RC HD BC SC TC UC VC WC XC CC"},H:{"2":"ID"},I:{"1":"DC J MD ZC ND OD","2":"I JD KD LD"},J:{"2":"D A"},K:{"1":"8B","2":"A B C H 7B YC"},L:{"2":"I"},M:{"2":"6B"},N:{"1":"B","2":"A"},O:{"2":"9B"},P:{"1":"J","2":"0 v w x y z PD QD RD SD TD KC UD VD WD XD YD AC BC CC ZD"},Q:{"2":"aD"},R:{"2":"bD"},S:{"1":"cD","2":"dD"}},B:7,C:"SPDY protocol",D:true};
