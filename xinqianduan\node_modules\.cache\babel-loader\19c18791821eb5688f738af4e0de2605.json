{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\Dashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\Dashboard.vue", "mtime": 1748604247131}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SystemMonitor", "name", "components", "data", "chartPeriod", "stats", "totalUsers", "totalCases", "totalContracts", "totalRevenue", "recentActivities", "id", "icon", "color", "title", "description", "time", "quickActions", "action", "todoList", "completed", "priority", "notifications", "read", "methods", "getCurrentTime", "now", "Date", "options", "year", "month", "day", "weekday", "toLocaleDateString", "handleQuickAction", "$message", "info", "$router", "push", "viewAllActivities", "viewAllTodos", "viewAllNotifications", "handleTodoChange", "todo", "success", "mark<PERSON><PERSON><PERSON>", "notification", "getPriorityText", "map", "high", "medium", "low"], "sources": ["src/views/pages/Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- 欢迎区域 -->\r\n    <div class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <h1 class=\"welcome-title\">欢迎使用法律服务管理系统</h1>\r\n        <p class=\"welcome-subtitle\">{{ getCurrentTime() }} | 管理员，您好！</p>\r\n      </div>\r\n      <div class=\"welcome-actions\">\r\n        <el-button type=\"primary\" @click=\"handleQuickAction('new-case')\">\r\n          <i class=\"el-icon-plus\"></i> 新建案件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleQuickAction('new-contract')\">\r\n          <i class=\"el-icon-document-add\"></i> 新建合同\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalUsers }}</div>\r\n              <div class=\"stat-label\">总用户数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon case-icon\">\r\n              <i class=\"el-icon-folder\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalCases }}</div>\r\n              <div class=\"stat-label\">案件总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon contract-icon\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalContracts }}</div>\r\n              <div class=\"stat-label\">合同数量</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon revenue-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ stats.totalRevenue }}</div>\r\n              <div class=\"stat-label\">总收入</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +22%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 左侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"16\" :lg=\"16\" :xl=\"16\">\r\n        <!-- 图表区域 -->\r\n        <div class=\"chart-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">业务数据趋势</span>\r\n              <div class=\"chart-controls\">\r\n                <el-radio-group v-model=\"chartPeriod\" size=\"small\">\r\n                  <el-radio-button label=\"week\">本周</el-radio-button>\r\n                  <el-radio-button label=\"month\">本月</el-radio-button>\r\n                  <el-radio-button label=\"year\">本年</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"chart-container\">\r\n              <div class=\"chart-placeholder\">\r\n                <i class=\"el-icon-data-line chart-icon\"></i>\r\n                <p>数据图表区域</p>\r\n                <p class=\"chart-desc\">这里可以集成 ECharts 或其他图表库显示业务数据趋势</p>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 最近活动 -->\r\n        <div class=\"activity-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">最近活动</span>\r\n              <el-button type=\"text\" @click=\"viewAllActivities\">查看全部</el-button>\r\n            </div>\r\n            <div class=\"activity-list\">\r\n              <div\r\n                v-for=\"activity in recentActivities\"\r\n                :key=\"activity.id\"\r\n                class=\"activity-item\"\r\n              >\r\n                <div class=\"activity-avatar\">\r\n                  <i :class=\"activity.icon\" :style=\"{ color: activity.color }\"></i>\r\n                </div>\r\n                <div class=\"activity-content\">\r\n                  <div class=\"activity-title\">{{ activity.title }}</div>\r\n                  <div class=\"activity-desc\">{{ activity.description }}</div>\r\n                  <div class=\"activity-time\">{{ activity.time }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 右侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n        <!-- 快捷操作 -->\r\n        <div class=\"quick-actions-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">快捷操作</span>\r\n            </div>\r\n            <div class=\"quick-actions\">\r\n              <div\r\n                v-for=\"action in quickActions\"\r\n                :key=\"action.id\"\r\n                class=\"quick-action-item\"\r\n                @click=\"handleQuickAction(action.action)\"\r\n              >\r\n                <div class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\r\n                  <i :class=\"action.icon\"></i>\r\n                </div>\r\n                <div class=\"action-content\">\r\n                  <div class=\"action-title\">{{ action.title }}</div>\r\n                  <div class=\"action-desc\">{{ action.description }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 待办事项 -->\r\n        <div class=\"todo-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">待办事项</span>\r\n              <el-badge :value=\"todoList.filter(item => !item.completed).length\" class=\"todo-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllTodos\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"todo-list\">\r\n              <div\r\n                v-for=\"todo in todoList.slice(0, 5)\"\r\n                :key=\"todo.id\"\r\n                class=\"todo-item\"\r\n                :class=\"{ completed: todo.completed }\"\r\n              >\r\n                <el-checkbox\r\n                  v-model=\"todo.completed\"\r\n                  @change=\"handleTodoChange(todo)\"\r\n                >\r\n                  {{ todo.title }}\r\n                </el-checkbox>\r\n                <div class=\"todo-priority\" :class=\"todo.priority\">\r\n                  {{ getPriorityText(todo.priority) }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 系统通知 -->\r\n        <div class=\"notification-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统通知</span>\r\n              <el-badge :value=\"notifications.filter(item => !item.read).length\" class=\"notification-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllNotifications\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"notification-list\">\r\n              <div\r\n                v-for=\"notification in notifications.slice(0, 3)\"\r\n                :key=\"notification.id\"\r\n                class=\"notification-item\"\r\n                :class=\"{ unread: !notification.read }\"\r\n                @click=\"markAsRead(notification)\"\r\n              >\r\n                <div class=\"notification-content\">\r\n                  <div class=\"notification-title\">{{ notification.title }}</div>\r\n                  <div class=\"notification-time\">{{ notification.time }}</div>\r\n                </div>\r\n                <div v-if=\"!notification.read\" class=\"notification-dot\"></div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- System Monitor -->\r\n        <div class=\"system-monitor-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统监控</span>\r\n            </div>\r\n            <div class=\"system-monitor-content\">\r\n              <system-monitor></system-monitor>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport SystemMonitor from '@/components/SystemMonitor.vue'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: {\r\n    SystemMonitor\r\n  },\r\n  data() {\r\n    return {\r\n      chartPeriod: 'month',\r\n      stats: {\r\n        totalUsers: 1248,\r\n        totalCases: 356,\r\n        totalContracts: 892,\r\n        totalRevenue: '2,456,789'\r\n      },\r\n      recentActivities: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#409EFF',\r\n          title: '新用户注册',\r\n          description: '张三注册了新账户',\r\n          time: '2分钟前'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document',\r\n          color: '#67C23A',\r\n          title: '合同审核完成',\r\n          description: '《服务合同-001》审核通过',\r\n          time: '15分钟前'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-folder-add',\r\n          color: '#E6A23C',\r\n          title: '新案件创建',\r\n          description: '李四创建了新的法律咨询案件',\r\n          time: '1小时前'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-money',\r\n          color: '#F56C6C',\r\n          title: '收款确认',\r\n          description: '收到客户王五的服务费用',\r\n          time: '2小时前'\r\n        }\r\n      ],\r\n      quickActions: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-plus',\r\n          color: '#409EFF',\r\n          title: '新建案件',\r\n          description: '创建新的法律案件',\r\n          action: 'new-case'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document-add',\r\n          color: '#67C23A',\r\n          title: '新建合同',\r\n          description: '创建新的合同文档',\r\n          action: 'new-contract'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#E6A23C',\r\n          title: '添加客户',\r\n          description: '添加新的客户信息',\r\n          action: 'new-client'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-upload',\r\n          color: '#F56C6C',\r\n          title: '文件归档',\r\n          description: '上传并归档文件',\r\n          action: 'upload-file'\r\n        }\r\n      ],\r\n      todoList: [\r\n        {\r\n          id: 1,\r\n          title: '审核张三的合同申请',\r\n          completed: false,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '回复李四的法律咨询',\r\n          completed: false,\r\n          priority: 'medium'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '准备明天的庭审材料',\r\n          completed: true,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '更新客户联系信息',\r\n          completed: false,\r\n          priority: 'low'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '整理本月财务报表',\r\n          completed: false,\r\n          priority: 'medium'\r\n        }\r\n      ],\r\n      notifications: [\r\n        {\r\n          id: 1,\r\n          title: '系统维护通知',\r\n          time: '今天 14:30',\r\n          read: false\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '新版本更新',\r\n          time: '昨天 16:20',\r\n          read: false\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '数据备份完成',\r\n          time: '昨天 09:15',\r\n          read: true\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    getCurrentTime() {\r\n      const now = new Date()\r\n      const options = {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric',\r\n        weekday: 'long'\r\n      }\r\n      return now.toLocaleDateString('zh-CN', options)\r\n    },\r\n\r\n    handleQuickAction(action) {\r\n      switch (action) {\r\n        case 'new-case':\r\n          this.$message.info('跳转到新建案件页面')\r\n          // this.$router.push('/cases/new')\r\n          break\r\n        case 'new-contract':\r\n          this.$message.info('跳转到新建合同页面')\r\n          // this.$router.push('/contracts/new')\r\n          break\r\n        case 'new-client':\r\n          this.$message.info('跳转到添加客户页面')\r\n          // this.$router.push('/clients/new')\r\n          break\r\n        case 'upload-file':\r\n          this.$message.info('跳转到文件归档页面')\r\n          this.$router.push('/archive/file')\r\n          break\r\n        default:\r\n          this.$message.info(`执行操作: ${action}`)\r\n      }\r\n    },\r\n\r\n    viewAllActivities() {\r\n      this.$message.info('查看所有活动')\r\n    },\r\n\r\n    viewAllTodos() {\r\n      this.$message.info('查看所有待办事项')\r\n    },\r\n\r\n    viewAllNotifications() {\r\n      this.$message.info('查看所有通知')\r\n    },\r\n\r\n    handleTodoChange(todo) {\r\n      this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\r\n    },\r\n\r\n    markAsRead(notification) {\r\n      notification.read = true\r\n      this.$message.success('通知已标记为已读')\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const map = {\r\n        high: '高',\r\n        medium: '中',\r\n        low: '低'\r\n      }\r\n      return map[priority] || '中'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 欢迎区域 */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  padding: 30px;\r\n  margin-bottom: 20px;\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n.case-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n.contract-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n.revenue-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.chart-section,\r\n.activity-section,\r\n.quick-actions-section,\r\n.todo-section,\r\n.notification-section,\r\n.system-monitor-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 图表区域 */\r\n.chart-container {\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  text-align: center;\r\n  color: #95a5a6;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.chart-desc {\r\n  margin: 8px 0 0 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 活动列表 */\r\n.activity-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.activity-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.activity-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.activity-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.activity-content {\r\n  flex: 1;\r\n}\r\n\r\n.activity-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-time {\r\n  color: #bdc3c7;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 快捷操作 */\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-action-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  background-color: #f8f9fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quick-action-item:hover {\r\n  background-color: #e9ecef;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.action-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.action-desc {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 待办事项 */\r\n.todo-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.todo-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.todo-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.todo-item.completed {\r\n  opacity: 0.6;\r\n}\r\n\r\n.todo-priority {\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.todo-priority.high {\r\n  background-color: #fee;\r\n  color: #e74c3c;\r\n}\r\n\r\n.todo-priority.medium {\r\n  background-color: #fff3cd;\r\n  color: #f39c12;\r\n}\r\n\r\n.todo-priority.low {\r\n  background-color: #d4edda;\r\n  color: #27ae60;\r\n}\r\n\r\n/* 通知列表 */\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.notification-item:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.notification-item.unread {\r\n  background-color: #e3f2fd;\r\n}\r\n\r\n.notification-content {\r\n  flex: 1;\r\n}\r\n\r\n.notification-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-time {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #409EFF;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .welcome-section {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 20px;\r\n  }\r\n\r\n  .welcome-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 12px;\r\n  }\r\n}\r\n</style> "], "mappings": ";AA2OA,OAAAA,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,WAAA;MACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;QACAC,YAAA;MACA;MACAC,gBAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,IAAA;MACA,EACA;MACAC,YAAA,GACA;QACAN,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAG,MAAA;MACA,GACA;QACAP,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAG,MAAA;MACA,GACA;QACAP,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAG,MAAA;MACA,GACA;QACAP,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAG,MAAA;MACA,EACA;MACAC,QAAA,GACA;QACAR,EAAA;QACAG,KAAA;QACAM,SAAA;QACAC,QAAA;MACA,GACA;QACAV,EAAA;QACAG,KAAA;QACAM,SAAA;QACAC,QAAA;MACA,GACA;QACAV,EAAA;QACAG,KAAA;QACAM,SAAA;QACAC,QAAA;MACA,GACA;QACAV,EAAA;QACAG,KAAA;QACAM,SAAA;QACAC,QAAA;MACA,GACA;QACAV,EAAA;QACAG,KAAA;QACAM,SAAA;QACAC,QAAA;MACA,EACA;MACAC,aAAA,GACA;QACAX,EAAA;QACAG,KAAA;QACAE,IAAA;QACAO,IAAA;MACA,GACA;QACAZ,EAAA;QACAG,KAAA;QACAE,IAAA;QACAO,IAAA;MACA,GACA;QACAZ,EAAA;QACAG,KAAA;QACAE,IAAA;QACAO,IAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,MAAAC,GAAA,OAAAC,IAAA;MACA,MAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;MACA;MACA,OAAAN,GAAA,CAAAO,kBAAA,UAAAL,OAAA;IACA;IAEAM,kBAAAhB,MAAA;MACA,QAAAA,MAAA;QACA;UACA,KAAAiB,QAAA,CAAAC,IAAA;UACA;UACA;QACA;UACA,KAAAD,QAAA,CAAAC,IAAA;UACA;UACA;QACA;UACA,KAAAD,QAAA,CAAAC,IAAA;UACA;UACA;QACA;UACA,KAAAD,QAAA,CAAAC,IAAA;UACA,KAAAC,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAH,QAAA,CAAAC,IAAA,UAAAlB,MAAA;MACA;IACA;IAEAqB,kBAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;IACA;IAEAI,aAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;IAEAK,qBAAA;MACA,KAAAN,QAAA,CAAAC,IAAA;IACA;IAEAM,iBAAAC,IAAA;MACA,KAAAR,QAAA,CAAAS,OAAA,CAAAD,IAAA,CAAAvB,SAAA;IACA;IAEAyB,WAAAC,YAAA;MACAA,YAAA,CAAAvB,IAAA;MACA,KAAAY,QAAA,CAAAS,OAAA;IACA;IAEAG,gBAAA1B,QAAA;MACA,MAAA2B,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACA,OAAAH,GAAA,CAAA3B,QAAA;IACA;EACA;AACA", "ignoreList": []}]}