import axios from "axios";
import { Message } from "element-ui";
import router from "../router";
import store from "../store";

// 纯前端模式 - 模拟API响应
const MOCK_MODE = false;

// 模拟数据
const mockData = {
  token: "mock-token-123456",
  user: {
    id: 1,
    name: "管理员",
    role: "admin"
  }
};

// 模拟API响应函数
const mockResponse = (data, delay = 300) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: "success",
        data: data
      });
    }, delay);
  });
};

if (!MOCK_MODE) {
  // 根据当前环境自动设置API地址
  const isProduction = window.location.hostname !== 'localhost';
  axios.defaults.baseURL = "http://*************/fdbadmin/public";

  axios.interceptors.response.use(
    (success) => {
      return success.data;
    },
    (error) => {
      if (error.response.status == 504 || error.response.status == 404) {
        Message.error({ message: "服务器被吃了( ╯□╰ )" });
      } else if (error.response.status == 403) {
        Message.error({ message: "权限不足，请联系管理员" });
      } else if (error.response.status == 401) {
        router.replace("/");
      } else {
        if (error.response.data.msg) {
          Message.error({ message: error.response.data.msg });
        } else {
          Message.error({ message: "未知错误!" });
        }
      }
      return;
    }
  );
}

let base = "/admin";

export const postKeyValueRequest = (url, params) => {
  if (MOCK_MODE) {
    // 模拟不同的API响应
    if (url.includes('/Login/checkToken')) {
      return mockResponse({ valid: true });
    }
    return mockResponse(mockData);
  }

  return axios({
    method: "post",
    url: `${base}${url}`,
    data: params,
    transformRequest: [
      function (data) {
        let ret = "";
        for (let i in data) {
          ret +=
            encodeURIComponent(i) + "=" + encodeURIComponent(data[i]) + "&";
        }
        return ret;
      },
    ],
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "Request-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const postRequest = (url, params) => {
  if (MOCK_MODE) {
    // 模拟不同的API响应
    if (url.includes('/Login/checkToken')) {
      return mockResponse({ valid: true });
    }
    return mockResponse(mockData);
  }

  return axios({
    method: "post",
    url: `${base}${url}`,
    data: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const putRequest = (url, params) => {
  if (MOCK_MODE) {
    return mockResponse(mockData);
  }

  return axios({
    method: "put",
    url: `${base}${url}`,
    data: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const getRequest = (url, params) => {
  if (MOCK_MODE) {
    return mockResponse(mockData);
  }

  return axios({
    method: "get",
    url: `${base}${url}`,
    params: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const deleteRequest = (url, params) => {
  if (MOCK_MODE) {
    return mockResponse({ success: true });
  }

  return axios({
    method: "delete",
    url: `${base}${url}`,
    params: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};
