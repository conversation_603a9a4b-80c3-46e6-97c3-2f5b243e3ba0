import axios from "axios";
import { Message } from "element-ui";
import router from "../router";
import store from "../store";

// 纯前端模式 - 模拟API响应
const MOCK_MODE = false;

// 模拟数据
const mockData = {
  token: "mock-token-123456",
  user: {
    id: 1,
    name: "管理员",
    role: "admin"
  }
};

// 模拟API响应函数
const mockResponse = (data, delay = 300) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: "success",
        data: data
      });
    }, delay);
  });
};

if (!MOCK_MODE) {
  // 根据当前环境自动设置API地址
  const isProduction = window.location.hostname !== 'localhost';
  axios.defaults.baseURL = isProduction
    ? "http://*************/fdbfront/fdbadmin/public"
    : "http://localhost/fdbfront/fdbadmin/public";

  axios.interceptors.response.use(
    (success) => {
      return success.data;
    },
    (error) => {
      if (error.response.status == 504 || error.response.status == 404) {
        Message.error({ message: "服务器被吃了( ╯□╰ )" });
      } else if (error.response.status == 403) {
        Message.error({ message: "权限不足，请联系管理员" });
      } else if (error.response.status == 401) {
        router.replace("/");
      } else {
        if (error.response.data.msg) {
          Message.error({ message: error.response.data.msg });
        } else {
          Message.error({ message: "未知错误!" });
        }
      }
      return;
    }
  );
}

let base = "/admin";

export const postKeyValueRequest = (url, params) => {
  if (MOCK_MODE) {
    // 模拟不同的API响应
    if (url.includes('/Login/checkToken')) {
      return mockResponse({ valid: true });
    }
    return mockResponse(mockData);
  }

  return axios({
    method: "post",
    url: `${base}${url}`,
    data: params,
    transformRequest: [
      function (data) {
        let ret = "";
        for (let i in data) {
          ret +=
            encodeURIComponent(i) + "=" + encodeURIComponent(data[i]) + "&";
        }
        return ret;
      },
    ],
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "Request-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const postRequest = (url, params) => {
  if (MOCK_MODE) {
    // 模拟不同的API响应
    if (url.includes('/Login/checkToken')) {
      return mockResponse({ valid: true });
    }
    return mockResponse(mockData);
  }

  return axios({
    method: "post",
    url: `${base}${url}`,
    data: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const putRequest = (url, params) => {
  if (MOCK_MODE) {
    return mockResponse(mockData);
  }

  return axios({
    method: "put",
    url: `${base}${url}`,
    data: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const getRequest = (url, params) => {
  if (MOCK_MODE) {
    // Dashboard API 模拟数据
    if (url.includes('/dashboard/getStats')) {
      return mockResponse({
        totalUsers: 1248,
        totalDebts: 356,
        totalOrders: 892,
        totalRevenue: '2,456,789',
        todayUsers: 12,
        todayOrders: 8,
        todayDebts: 15,
        monthUsers: 156,
        monthOrders: 89,
        monthRevenue: '456,789',
        pendingDebts: 23,
        processingDebts: 45,
        litigationDebts: 12,
        closedDebts: 276,
        pendingOrders: 15,
        paidOrders: 877,
        refundOrders: 0,
        totalDebtAmount: '15,678,900',
        totalBackAmount: '8,234,567',
        remainingAmount: '7,444,333',
        recoveryRate: 52.5
      });
    }
    if (url.includes('/dashboard/getActivities')) {
      return mockResponse([
        {
          id: 'user_1',
          type: 'user_register',
          icon: 'el-icon-user-solid',
          color: '#409EFF',
          title: '新用户注册',
          description: '张三 注册了账户',
          time: '2分钟前'
        },
        {
          id: 'order_1',
          type: 'order_create',
          icon: 'el-icon-shopping-cart-2',
          color: '#67C23A',
          title: '新订单创建',
          description: '订单 DD20250110001 已创建，金额：¥5,000',
          time: '15分钟前'
        },
        {
          id: 'debt_1',
          type: 'debt_submit',
          icon: 'el-icon-document',
          color: '#E6A23C',
          title: '债务提交',
          description: '债务人 李四 的债务已提交，金额：¥50,000.00',
          time: '1小时前'
        }
      ]);
    }
    if (url.includes('/dashboard/getTodos')) {
      return mockResponse([
        {
          id: 'debt_1',
          type: 'debt',
          title: '处理债务：张三',
          description: '债务金额：¥50,000.00',
          priority: 'high',
          completed: false,
          url: '/debt/detail/1'
        },
        {
          id: 'order_1',
          type: 'order',
          title: '跟进订单：DD20250110001',
          description: '订单金额：¥5,000',
          priority: 'medium',
          completed: false,
          url: '/order/detail/1'
        }
      ]);
    }
    if (url.includes('/dashboard/getQuickActions')) {
      return mockResponse([
        {
          title: '添加用户',
          icon: 'el-icon-user-solid',
          color: '#409EFF',
          url: '/user',
          count: 1248
        },
        {
          title: '新增债务',
          icon: 'el-icon-document',
          color: '#E6A23C',
          url: '/debts',
          count: 23
        },
        {
          title: '订单管理',
          icon: 'el-icon-shopping-cart-2',
          color: '#67C23A',
          url: '/dingdan',
          count: 15
        },
        {
          title: '聊天管理',
          icon: 'el-icon-chat-dot-round',
          color: '#F56C6C',
          url: '/chat',
          count: 5
        }
      ]);
    }
    return mockResponse(mockData);
  }

  return axios({
    method: "get",
    url: `${base}${url}`,
    params: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};

export const deleteRequest = (url, params) => {
  if (MOCK_MODE) {
    return mockResponse({ success: true });
  }

  return axios({
    method: "delete",
    url: `${base}${url}`,
    params: params,
    headers: {
      "Login-Token": store.getters.GET_TOKEN || "",
    },
  });
};
