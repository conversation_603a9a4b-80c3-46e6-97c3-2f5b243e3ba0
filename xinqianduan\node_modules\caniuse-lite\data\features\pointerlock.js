module.exports={A:{A:{"2":"K D E F A B aC"},B:{"1":"1 2 3 4 5 6 7 8 9 L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I","2":"C"},C:{"1":"1 2 3 4 5 6 7 8 9 fB gB hB iB jB kB lB mB nB EC oB FC pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R GC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I 6B HC IC cC dC","2":"bC DC J FB K D E F A B C L eC fC","33":"0 M G N O P GB v w x y z HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB"},D:{"1":"1 2 3 4 5 6 7 8 9 SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB EC oB FC pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB I 6B HC IC","2":"J FB K D E F A B C L M G","33":"0 x y z HB IB JB KB LB MB NB OB PB QB RB","66":"N O P GB v w"},E:{"1":"B C L M G KC 7B 8B lC mC nC LC MC 9B oC AC NC OC PC QC RC pC BC SC TC UC VC WC XC CC qC","2":"J FB K D E F A gC JC hC iC jC kC"},F:{"1":"0 z HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B Q H R GC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u","2":"F B C rC sC tC uC 7B YC vC 8B","33":"G N O P GB v w x y"},G:{"2":"E JC wC ZC xC yC zC 0C 1C 2C 3C 4C 5C 6C 7C 8C 9C AD BD CD DD ED FD LC MC 9B GD AC NC OC PC QC RC HD BC SC TC UC VC WC XC CC"},H:{"2":"ID"},I:{"2":"DC J I JD KD LD MD ZC ND OD"},J:{"2":"D A"},K:{"2":"A B C 7B YC 8B","16":"H"},L:{"2":"I"},M:{"1":"6B"},N:{"2":"A B"},O:{"16":"9B"},P:{"2":"0 J v w x y z PD QD RD SD TD KC UD VD WD XD YD AC BC CC ZD"},Q:{"16":"aD"},R:{"1":"bD"},S:{"1":"cD dD"}},B:2,C:"Pointer Lock API",D:true};
